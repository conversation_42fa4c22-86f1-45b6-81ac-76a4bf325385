{% extends "base.html" %}

{% block title %}تعديل المستخدم - Edit User{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-edit me-2"></i>
        تعديل المستخدم: {{ user.full_name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('users') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    تعديل بيانات المستخدم
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                            {% if form.username.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.full_name.label(class="form-label") }}
                            {{ form.full_name(class="form-control" + (" is-invalid" if form.full_name.errors else "")) }}
                            {% if form.full_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.full_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.department.label(class="form-label") }}
                            {{ form.department(class="form-control" + (" is-invalid" if form.department.errors else "")) }}
                            {% if form.department.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.department.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.role.label(class="form-label") }}
                            {{ form.role(class="form-select" + (" is-invalid" if form.role.errors else "")) }}
                            {% if form.role.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.role.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الحالة</label>
                            <div class="form-check">
                                {{ form.is_active(class="form-check-input") }}
                                {{ form.is_active.label(class="form-check-label") }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <div>
                            {{ form.submit(class="btn btn-primary btn-lg") }}
                            <a href="{{ url_for('users') }}" class="btn btn-secondary btn-lg ms-2">إلغاء</a>
                        </div>
                        <div>
                            {% if user.id != current_user.id %}
                            <button type="button" class="btn btn-warning" onclick="resetPassword()">
                                <i class="fas fa-key me-1"></i>
                                إعادة تعيين كلمة المرور
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- معلومات المستخدم -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات المستخدم
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>تاريخ التسجيل:</strong></td>
                        <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    <tr>
                        <td><strong>آخر دخول:</strong></td>
                        <td>
                            {% if user.last_login %}
                                {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}
                            {% else %}
                                <span class="text-muted">لم يسجل دخول</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>الرسائل المرسلة:</strong></td>
                        <td>{{ user.sent_messages.count() }}</td>
                    </tr>
                    <tr>
                        <td><strong>الرسائل المستلمة:</strong></td>
                        <td>{{ user.received_messages.count() }}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- أدوار المستخدمين -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-users-cog me-2"></i>
                    أدوار المستخدمين
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <span class="badge bg-danger me-2">مدير</span>
                        جميع الصلاحيات + إدارة المستخدمين
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-warning me-2">سكرتير</span>
                        إرسال واستقبال + أرشفة + عرض القسم
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-primary me-2">موظف</span>
                        إرسال واستقبال الرسائل الخاصة
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- تحذيرات -->
        {% if user.role.value == 'admin' %}
        <div class="card mt-3 border-warning">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تحذير
                </h6>
            </div>
            <div class="card-body">
                <p class="small mb-0">
                    هذا المستخدم مدير في النظام. تأكد من صحة التعديلات قبل الحفظ.
                </p>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal for password reset -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إعادة تعيين كلمة المرور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من إعادة تعيين كلمة مرور المستخدم <strong>{{ user.full_name }}</strong>؟</p>
                <p class="text-info">
                    <i class="fas fa-info-circle me-1"></i>
                    سيتم إنشاء كلمة مرور جديدة وإرسالها للمستخدم.
                </p>
                <div class="mb-3">
                    <label class="form-label">كلمة المرور الجديدة:</label>
                    <input type="text" class="form-control" id="newPassword" placeholder="أدخل كلمة المرور الجديدة">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="confirmResetPassword()">إعادة تعيين</button>
            </div>
        </div>
    </div>
</div>

<script>
function resetPassword() {
    var resetModal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
    resetModal.show();
}

function confirmResetPassword() {
    var newPassword = document.getElementById('newPassword').value;
    if (newPassword.length < 6) {
        alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        return;
    }
    
    // هنا يمكن إضافة AJAX request لإعادة تعيين كلمة المرور
    alert('تم إعادة تعيين كلمة المرور بنجاح');
    
    var resetModal = bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal'));
    resetModal.hide();
}
</script>

<style>
    .form-control:focus,
    .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
    }
    
    .btn-primary:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
</style>
{% endblock %}
