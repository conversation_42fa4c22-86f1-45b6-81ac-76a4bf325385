{% extends "base.html" %}

{% block title %}الرد على الرسالة - Reply Message{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-reply me-2"></i>
        الرد على الرسالة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('view_message', id=original_message.id) }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للرسالة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- نموذج الرد -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    كتابة الرد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.recipient_id.label(class="form-label") }}
                            {{ form.recipient_id(class="form-select", readonly=true, disabled=true) }}
                            <div class="form-text">المرسل إليه محدد تلقائياً</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.classification.label(class="form-label") }}
                            {{ form.classification(class="form-select" + (" is-invalid" if form.classification.errors else "")) }}
                            {% if form.classification.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.classification.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.subject.label(class="form-label") }}
                        {{ form.subject(class="form-control" + (" is-invalid" if form.subject.errors else ""), readonly=true) }}
                        {% if form.subject.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.subject.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">الموضوع محدد تلقائياً كرد على الرسالة الأصلية</div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.content.label(class="form-label") }}
                        {{ form.content(class="form-control" + (" is-invalid" if form.content.errors else ""), rows="8", placeholder="اكتب ردك هنا...") }}
                        {% if form.content.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.content.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <div>
                            {{ form.submit(class="btn btn-primary btn-lg") }}
                            <a href="{{ url_for('view_message', id=original_message.id) }}" class="btn btn-secondary btn-lg ms-2">إلغاء</a>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-info" onclick="toggleOriginalMessage()">
                                <i class="fas fa-eye me-1"></i>
                                عرض/إخفاء الرسالة الأصلية
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- الرسالة الأصلية -->
        <div class="card mt-3" id="originalMessage" style="display: none;">
            <div class="card-header bg-light">
                <h6 class="mb-0">
                    <i class="fas fa-envelope me-2"></i>
                    الرسالة الأصلية
                </h6>
            </div>
            <div class="card-body">
                <div class="row mb-2">
                    <div class="col-md-6">
                        <strong>من:</strong> {{ original_message.sender.full_name }}
                    </div>
                    <div class="col-md-6">
                        <strong>التاريخ:</strong> {{ original_message.created_at.strftime('%Y-%m-%d %H:%M') }}
                    </div>
                </div>
                <div class="mb-2">
                    <strong>الموضوع:</strong> {{ original_message.subject }}
                </div>
                <div class="mb-2">
                    <strong>التصنيف:</strong>
                    {% if original_message.classification.value == 'urgent' %}
                        <span class="badge bg-danger">عاجل</span>
                    {% elif original_message.classification.value == 'secret' %}
                        <span class="badge bg-warning">سري</span>
                    {% else %}
                        <span class="badge bg-primary">عادي</span>
                    {% endif %}
                </div>
                <hr>
                <div class="bg-light p-3 rounded" style="white-space: pre-wrap;">
                    {{ original_message.content }}
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- معلومات الرد -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الرد
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>المرسل إليه:</strong></td>
                        <td>{{ original_message.sender.full_name }}</td>
                    </tr>
                    <tr>
                        <td><strong>القسم:</strong></td>
                        <td>{{ original_message.sender.department or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td><strong>رقم الرسالة الأصلية:</strong></td>
                        <td>{{ original_message.message_number }}</td>
                    </tr>
                    <tr>
                        <td><strong>تاريخ الاستلام:</strong></td>
                        <td>{{ original_message.created_at.strftime('%Y-%m-%d') }}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- نصائح للرد -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح للرد
                </h6>
            </div>
            <div class="card-body">
                <ul class="small mb-0">
                    <li class="mb-2">اقرأ الرسالة الأصلية بعناية قبل الرد</li>
                    <li class="mb-2">كن واضحاً ومختصراً في ردك</li>
                    <li class="mb-2">اختر التصنيف المناسب للرد</li>
                    <li class="mb-2">تأكد من الإجابة على جميع النقاط المطروحة</li>
                    <li class="mb-2">راجع الرد قبل الإرسال</li>
                </ul>
            </div>
        </div>
        
        <!-- تصنيفات الرسائل -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tags me-2"></i>
                    تصنيفات الرسائل
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <span class="badge bg-primary me-2">عادي</span>
                    للرسائل العادية والمعلومات العامة
                </div>
                <div class="mb-2">
                    <span class="badge bg-warning me-2">سري</span>
                    للمعلومات الحساسة والسرية
                </div>
                <div class="mb-2">
                    <span class="badge bg-danger me-2">عاجل</span>
                    للرسائل التي تتطلب رد سريع
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleOriginalMessage() {
    const originalMessage = document.getElementById('originalMessage');
    const button = event.target;
    
    if (originalMessage.style.display === 'none') {
        originalMessage.style.display = 'block';
        button.innerHTML = '<i class="fas fa-eye-slash me-1"></i>إخفاء الرسالة الأصلية';
    } else {
        originalMessage.style.display = 'none';
        button.innerHTML = '<i class="fas fa-eye me-1"></i>عرض الرسالة الأصلية';
    }
}

// Auto-resize textarea
document.addEventListener('DOMContentLoaded', function() {
    const textarea = document.querySelector('textarea[name="content"]');
    if (textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    }
});
</script>

<style>
    .form-control:focus,
    .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
    }
    
    .btn-primary:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    
    textarea {
        resize: vertical;
        min-height: 150px;
    }
    
    .bg-light {
        background-color: #f8f9fa !important;
    }
</style>
{% endblock %}
