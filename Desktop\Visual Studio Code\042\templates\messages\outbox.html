{% extends "base.html" %}

{% block title %}الرسائل الصادرة - Outbox{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-paper-plane me-2"></i>
        الرسائل الصادرة
        {% if messages.total > 0 %}
            <span class="badge bg-primary">{{ messages.total }}</span>
        {% endif %}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('compose') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                رسالة جديدة
            </a>
        </div>
    </div>
</div>

{% if messages.items %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة الرسائل الصادرة</h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th width="20%">المستلم</th>
                        <th width="35%">الموضوع</th>
                        <th width="10%">التصنيف</th>
                        <th width="10%">الحالة</th>
                        <th width="15%">التاريخ</th>
                        <th width="10%">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for message in messages.items %}
                    <tr class="{{ 'table-warning' if message.classification.value == 'urgent' else 'table-info' if message.classification.value == 'secret' else '' }}">
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                    {{ message.recipient.full_name[0].upper() }}
                                </div>
                                <div>
                                    <div class="fw-semibold">{{ message.recipient.full_name }}</div>
                                    <small class="text-muted">{{ message.recipient.department or 'N/A' }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <a href="{{ url_for('view_message', id=message.id) }}" class="text-decoration-none">
                                <div class="fw-semibold">{{ message.subject }}</div>
                                <small class="text-muted">رقم: {{ message.message_number }}</small>
                            </a>
                        </td>
                        <td>
                            {% if message.classification.value == 'urgent' %}
                                <span class="badge badge-urgent">عاجل</span>
                            {% elif message.classification.value == 'secret' %}
                                <span class="badge badge-secret">سري</span>
                            {% else %}
                                <span class="badge badge-normal">عادي</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if message.status.value == 'new' %}
                                <span class="badge bg-warning">لم يُقرأ</span>
                            {% elif message.status.value == 'read' %}
                                <span class="badge bg-info">مقروء</span>
                            {% elif message.status.value == 'replied' %}
                                <span class="badge bg-success">تم الرد</span>
                            {% else %}
                                <span class="badge bg-secondary">مؤرشف</span>
                            {% endif %}
                        </td>
                        <td>
                            <div>{{ message.created_at.strftime('%Y-%m-%d') }}</div>
                            <small class="text-muted">{{ message.created_at.strftime('%H:%M') }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('view_message', id=message.id) }}" class="btn btn-outline-primary btn-sm" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if message.attachments.count() > 0 %}
                                <button class="btn btn-outline-info btn-sm" title="مرفقات">
                                    <i class="fas fa-paperclip"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Pagination -->
    {% if messages.pages > 1 %}
    <div class="card-footer">
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center mb-0">
                {% if messages.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('outbox', page=messages.prev_num) }}">السابق</a>
                    </li>
                {% endif %}
                
                {% for page_num in messages.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != messages.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('outbox', page=page_num) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if messages.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('outbox', page=messages.next_num) }}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}
</div>

{% else %}
<div class="text-center py-5">
    <i class="fas fa-paper-plane fa-4x text-muted mb-3"></i>
    <h4 class="text-muted">لا توجد رسائل صادرة</h4>
    <p class="text-muted">ستظهر هنا الرسائل التي أرسلتها للمستخدمين الآخرين</p>
    <a href="{{ url_for('compose') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>
        إنشاء رسالة جديدة
    </a>
</div>
{% endif %}

<style>
    .avatar-sm {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }
    
    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }
</style>
{% endblock %}
