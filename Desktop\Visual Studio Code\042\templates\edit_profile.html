{% extends "base.html" %}

{% block title %}تعديل الملف الشخصي - Edit Profile{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-edit me-2"></i>
        تعديل الملف الشخصي
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('profile') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    تعديل البيانات الشخصية
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                            {% if form.username.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">اسم المستخدم للدخول إلى النظام</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">البريد الإلكتروني للتواصل والإشعارات</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.full_name.label(class="form-label") }}
                            {{ form.full_name(class="form-control" + (" is-invalid" if form.full_name.errors else "")) }}
                            {% if form.full_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.full_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.department.label(class="form-label") }}
                            {{ form.department(class="form-control" + (" is-invalid" if form.department.errors else "")) }}
                            {% if form.department.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.department.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">القسم أو الإدارة التي تعمل بها</div>
                        </div>
                    </div>
                    
                    <!-- إعدادات إضافية للمدير -->
                    {% if current_user.has_permission('manage_users') %}
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.role.label(class="form-label") }}
                            {{ form.role(class="form-select" + (" is-invalid" if form.role.errors else "")) }}
                            {% if form.role.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.role.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">دورك في النظام وصلاحياتك</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">حالة الحساب</label>
                            <div class="form-check">
                                {{ form.is_active(class="form-check-input") }}
                                {{ form.is_active.label(class="form-check-label") }}
                            </div>
                            <div class="form-text">تفعيل أو إلغاء تفعيل الحساب</div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="d-flex justify-content-between">
                        <div>
                            {{ form.submit(class="btn btn-primary btn-lg") }}
                            <a href="{{ url_for('profile') }}" class="btn btn-secondary btn-lg ms-2">إلغاء</a>
                        </div>
                        <div>
                            <a href="{{ url_for('change_password') }}" class="btn btn-outline-warning">
                                <i class="fas fa-key me-1"></i>
                                تغيير كلمة المرور
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- معلومات الحساب -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الحساب
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>تاريخ التسجيل:</strong></td>
                        <td>{{ current_user.created_at.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    <tr>
                        <td><strong>آخر دخول:</strong></td>
                        <td>
                            {% if current_user.last_login %}
                                {{ current_user.last_login.strftime('%Y-%m-%d %H:%M') }}
                            {% else %}
                                <span class="text-muted">لم يسجل دخول</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>الدور الحالي:</strong></td>
                        <td>
                            {% if current_user.role.value == 'admin' %}
                                <span class="badge bg-danger">مدير</span>
                            {% elif current_user.role.value == 'secretary' %}
                                <span class="badge bg-warning">سكرتير</span>
                            {% else %}
                                <span class="badge bg-primary">موظف</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>حالة الحساب:</strong></td>
                        <td>
                            {% if current_user.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- نصائح -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح مهمة
                </h6>
            </div>
            <div class="card-body">
                <ul class="small mb-0">
                    <li class="mb-2">تأكد من صحة البريد الإلكتروني لاستلام الإشعارات</li>
                    <li class="mb-2">اختر اسم مستخدم واضح ومفهوم</li>
                    <li class="mb-2">حدد القسم بدقة لتسهيل التواصل</li>
                    <li class="mb-2">احرص على تحديث بياناتك بانتظام</li>
                    {% if current_user.has_permission('manage_users') %}
                    <li class="mb-2 text-warning">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        كونك مدير، تأكد من صحة الدور المحدد
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
        
        <!-- إحصائيات سريعة -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائياتك
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-2">
                        <h5 class="text-primary">{{ current_user.sent_messages.count() }}</h5>
                        <small class="text-muted">رسائل مرسلة</small>
                    </div>
                    <div class="col-6 mb-2">
                        <h5 class="text-success">{{ current_user.received_messages.count() }}</h5>
                        <small class="text-muted">رسائل مستلمة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .form-control:focus,
    .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
    }
    
    .btn-primary:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    
    .form-text {
        font-size: 0.875rem;
        color: #6c757d;
    }
</style>
{% endblock %}
