{% extends "base.html" %}

{% block title %}تغيير كلمة المرور - Change Password{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-key me-2"></i>
        تغيير كلمة المرور
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('profile') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-lock me-2"></i>
                            تغيير كلمة المرور
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="passwordForm">
                            {{ form.hidden_tag() }}

                            <div class="mb-3">
                                {{ form.current_password.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.current_password(class="form-control" + (" is-invalid" if form.current_password.errors else ""), id="currentPassword") }}
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('currentPassword')">
                                        <i class="fas fa-eye" id="currentPasswordIcon"></i>
                                    </button>
                                </div>
                                {% if form.current_password.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.current_password.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                {{ form.new_password.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.new_password(class="form-control" + (" is-invalid" if form.new_password.errors else ""), id="newPassword", oninput="checkPasswordStrength()") }}
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('newPassword')">
                                        <i class="fas fa-eye" id="newPasswordIcon"></i>
                                    </button>
                                </div>
                                {% if form.new_password.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.new_password.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}

                                <!-- Password strength indicator -->
                                <div class="mt-2">
                                    <div class="progress" style="height: 5px;">
                                        <div class="progress-bar" id="passwordStrength" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <small id="passwordStrengthText" class="form-text">قوة كلمة المرور</small>
                                </div>
                            </div>

                            <div class="mb-3">
                                {{ form.new_password2.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.new_password2(class="form-control" + (" is-invalid" if form.new_password2.errors else ""), id="confirmPassword", oninput="checkPasswordMatch()") }}
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirmPassword')">
                                        <i class="fas fa-eye" id="confirmPasswordIcon"></i>
                                    </button>
                                </div>
                                {% if form.new_password2.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.new_password2.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small id="passwordMatchText" class="form-text"></small>
                            </div>

                            <div class="d-flex justify-content-between">
                                <div>
                                    {{ form.submit(class="btn btn-primary btn-lg", id="submitBtn") }}
                                    <a href="{{ url_for('profile') }}" class="btn btn-secondary btn-lg ms-2">إلغاء</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- نصائح الأمان -->
                <div class="card border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            نصائح الأمان
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="small mb-0">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-1"></i>
                                استخدم 8 أحرف على الأقل
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-1"></i>
                                امزج بين الأحرف الكبيرة والصغيرة
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-1"></i>
                                أضف أرقام ورموز خاصة
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-times text-danger me-1"></i>
                                تجنب المعلومات الشخصية
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-times text-danger me-1"></i>
                                لا تشارك كلمة المرور
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-clock text-warning me-1"></i>
                                غير كلمة المرور دورياً
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- معلومات الحساب -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            معلومات الحساب
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>المستخدم:</strong></td>
                                <td>{{ current_user.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>اسم المستخدم:</strong></td>
                                <td>{{ current_user.username }}</td>
                            </tr>
                            <tr>
                                <td><strong>آخر دخول:</strong></td>
                                <td>
                                    {% if current_user.last_login %}
                                        {{ current_user.last_login.strftime('%Y-%m-%d %H:%M') }}
                                    {% else %}
                                        <span class="text-muted">لم يسجل دخول</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + 'Icon');

    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function checkPasswordStrength() {
    const password = document.getElementById('newPassword').value;
    const strengthBar = document.getElementById('passwordStrength');
    const strengthText = document.getElementById('passwordStrengthText');

    let strength = 0;
    let feedback = [];

    // Length check
    if (password.length >= 8) strength += 25;
    else feedback.push('8 أحرف على الأقل');

    // Uppercase check
    if (/[A-Z]/.test(password)) strength += 25;
    else feedback.push('حرف كبير');

    // Lowercase check
    if (/[a-z]/.test(password)) strength += 25;
    else feedback.push('حرف صغير');

    // Number or special character check
    if (/[0-9]/.test(password) || /[^A-Za-z0-9]/.test(password)) strength += 25;
    else feedback.push('رقم أو رمز');

    // Update progress bar
    strengthBar.style.width = strength + '%';

    if (strength < 50) {
        strengthBar.className = 'progress-bar bg-danger';
        strengthText.textContent = 'ضعيف - يحتاج: ' + feedback.join(', ');
        strengthText.className = 'form-text text-danger';
    } else if (strength < 75) {
        strengthBar.className = 'progress-bar bg-warning';
        strengthText.textContent = 'متوسط - يحتاج: ' + feedback.join(', ');
        strengthText.className = 'form-text text-warning';
    } else {
        strengthBar.className = 'progress-bar bg-success';
        strengthText.textContent = 'قوي - كلمة مرور ممتازة!';
        strengthText.className = 'form-text text-success';
    }

    checkPasswordMatch();
}

function checkPasswordMatch() {
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const matchText = document.getElementById('passwordMatchText');
    const submitBtn = document.getElementById('submitBtn');

    if (confirmPassword === '') {
        matchText.textContent = '';
        matchText.className = 'form-text';
        return;
    }

    if (newPassword === confirmPassword) {
        matchText.textContent = '✓ كلمات المرور متطابقة';
        matchText.className = 'form-text text-success';
        submitBtn.disabled = false;
    } else {
        matchText.textContent = '✗ كلمات المرور غير متطابقة';
        matchText.className = 'form-text text-danger';
        submitBtn.disabled = true;
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    checkPasswordStrength();
    checkPasswordMatch();
});
</script>

<style>
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
    }

    .btn-primary:hover:not(:disabled) {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .btn-primary:disabled {
        background: #6c757d;
        opacity: 0.6;
    }

    .input-group .btn {
        border-left: none;
    }

    .progress {
        border-radius: 10px;
    }

    .progress-bar {
        transition: width 0.3s ease;
    }
</style>
{% endblock %}
