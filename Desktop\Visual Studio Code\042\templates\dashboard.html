{% extends "base.html" %}

{% block title %}لوحة التحكم - Dashboard{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>
        لوحة التحكم
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('compose') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                رسالة جديدة
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            إجمالي الرسائل
                        </div>
                        <div class="stats-number">{{ total_messages }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-envelope fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            رسائل جديدة
                        </div>
                        <div class="stats-number">{{ new_messages }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-inbox fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card" style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); color: white;">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            رسائل عاجلة
                        </div>
                        <div class="stats-number">{{ urgent_messages }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); color: white;">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            الإشعارات
                        </div>
                        <div class="stats-number">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-bell fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Messages -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    الرسائل الحديثة
                </h5>
            </div>
            <div class="card-body">
                {% if recent_messages %}
                    <div class="list-group list-group-flush">
                        {% for message in recent_messages %}
                        <div class="list-group-item message-item {{ 'message-urgent' if message.classification.value == 'urgent' else 'message-secret' if message.classification.value == 'secret' else '' }}">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <a href="{{ url_for('view_message', id=message.id) }}" class="text-decoration-none">
                                        {{ message.subject }}
                                    </a>
                                    {% if message.classification.value == 'urgent' %}
                                        <span class="badge badge-urgent ms-2">عاجل</span>
                                    {% elif message.classification.value == 'secret' %}
                                        <span class="badge badge-secret ms-2">سري</span>
                                    {% endif %}
                                </h6>
                                <small class="text-muted">{{ message.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                            <p class="mb-1">من: {{ message.sender.full_name }}</p>
                            <small class="text-muted">رقم الرسالة: {{ message.message_number }}</small>
                            {% if message.status.value == 'new' %}
                                <span class="badge bg-success ms-2">جديد</span>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('inbox') }}" class="btn btn-outline-primary">
                            عرض جميع الرسائل
                            <i class="fas fa-arrow-left ms-1"></i>
                        </a>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد رسائل حديثة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Notifications -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bell me-2"></i>
                    الإشعارات
                </h5>
            </div>
            <div class="card-body">
        
                <div class="text-center py-4">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد إشعارات جديدة</p>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('compose') }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>
                        إنشاء رسالة جديدة
                    </a>
                    <a href="{{ url_for('inbox') }}" class="btn btn-outline-success">
                        <i class="fas fa-inbox me-2"></i>
                        الرسائل الواردة
                    </a>
                    <a href="{{ url_for('search') }}" class="btn btn-outline-info">
                        <i class="fas fa-search me-2"></i>
                        البحث المتقدم
                    </a>
                    {% if current_user.has_permission('reports') %}
                    <a href="{{ url_for('reports') }}" class="btn btn-outline-warning">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Welcome Message for New Users -->
{% if current_user.last_login is none or (current_user.last_login and (current_user.created_at - current_user.last_login).days < 1) %}
<div class="alert alert-info alert-dismissible fade show mt-4" role="alert">
    <h5 class="alert-heading">
        <i class="fas fa-star me-2"></i>
        مرحباً بك في نظام المراسلات الداخلية!
    </h5>
    <p>يمكنك الآن إرسال واستقبال الرسائل الداخلية بسهولة. استخدم القائمة الجانبية للتنقل بين الأقسام المختلفة.</p>
    <hr>
    <p class="mb-0">
        <strong>نصائح:</strong>
        • استخدم التصنيفات (عادي، سري، عاجل) لتنظيم رسائلك
        • يمكنك إرفاق ملفات مع رسائلك
        • استخدم البحث المتقدم للعثور على رسائل محددة
    </p>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
{% endif %}
{% endblock %}
