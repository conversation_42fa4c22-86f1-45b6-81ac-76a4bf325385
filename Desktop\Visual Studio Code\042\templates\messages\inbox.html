{% extends "base.html" %}

{% block title %}الرسائل الواردة - Inbox{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-inbox me-2"></i>
        الرسائل الواردة
        {% if messages.total > 0 %}
            <span class="badge bg-primary">{{ messages.total }}</span>
        {% endif %}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('compose') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                رسالة جديدة
            </a>
            <a href="{{ url_for('search') }}" class="btn btn-outline-info">
                <i class="fas fa-search me-1"></i>
                بحث
            </a>
        </div>
    </div>
</div>

{% if messages.items %}
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">قائمة الرسائل الواردة</h5>
            </div>
            <div class="col-auto">
                <div class="btn-group btn-group-sm" role="group">
                    <input type="radio" class="btn-check" name="filter" id="all" autocomplete="off" checked>
                    <label class="btn btn-outline-primary" for="all">الكل</label>
                    
                    <input type="radio" class="btn-check" name="filter" id="new" autocomplete="off">
                    <label class="btn btn-outline-success" for="new">جديد</label>
                    
                    <input type="radio" class="btn-check" name="filter" id="urgent" autocomplete="off">
                    <label class="btn btn-outline-danger" for="urgent">عاجل</label>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th width="5%">
                            <input type="checkbox" class="form-check-input" id="selectAll">
                        </th>
                        <th width="15%">المرسل</th>
                        <th width="35%">الموضوع</th>
                        <th width="10%">التصنيف</th>
                        <th width="10%">الحالة</th>
                        <th width="15%">التاريخ</th>
                        <th width="10%">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for message in messages.items %}
                    <tr class="message-row {{ 'table-warning' if message.classification.value == 'urgent' else 'table-info' if message.classification.value == 'secret' else '' }} {{ 'fw-bold' if message.status.value == 'new' else '' }}"
                        data-classification="{{ message.classification.value }}"
                        data-status="{{ message.status.value }}">
                        <td>
                            <input type="checkbox" class="form-check-input message-checkbox" value="{{ message.id }}">
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                    {{ message.sender.full_name[0].upper() }}
                                </div>
                                <div>
                                    <div class="fw-semibold">{{ message.sender.full_name }}</div>
                                    <small class="text-muted">{{ message.sender.department or 'N/A' }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <a href="{{ url_for('view_message', id=message.id) }}" class="text-decoration-none">
                                <div class="fw-semibold">{{ message.subject }}</div>
                                <small class="text-muted">رقم: {{ message.message_number }}</small>
                            </a>
                        </td>
                        <td>
                            {% if message.classification.value == 'urgent' %}
                                <span class="badge badge-urgent">عاجل</span>
                            {% elif message.classification.value == 'secret' %}
                                <span class="badge badge-secret">سري</span>
                            {% else %}
                                <span class="badge badge-normal">عادي</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                {% if message.status.value == 'new' %}
                                    <span class="badge bg-success me-2">جديد</span>
                                {% elif message.status.value == 'read' %}
                                    <span class="badge bg-info me-2">مقروء</span>
                                {% elif message.status.value == 'replied' %}
                                    <span class="badge bg-warning me-2">تم الرد</span>
                                {% else %}
                                    <span class="badge bg-secondary me-2">مؤرشف</span>
                                {% endif %}

                                <!-- زر تغيير سريع للحالة -->
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" title="تغيير الحالة">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        {% if message.status.value != 'new' %}
                                        <li><a class="dropdown-item" href="#" onclick="changeStatus({{ message.id }}, 'new')">
                                            <i class="fas fa-circle text-success me-2"></i>جديد
                                        </a></li>
                                        {% endif %}
                                        {% if message.status.value != 'read' %}
                                        <li><a class="dropdown-item" href="#" onclick="changeStatus({{ message.id }}, 'read')">
                                            <i class="fas fa-circle text-info me-2"></i>مقروء
                                        </a></li>
                                        {% endif %}
                                        {% if message.status.value != 'replied' %}
                                        <li><a class="dropdown-item" href="#" onclick="changeStatus({{ message.id }}, 'replied')">
                                            <i class="fas fa-circle text-warning me-2"></i>تم الرد
                                        </a></li>
                                        {% endif %}
                                        {% if message.status.value != 'archived' %}
                                        <li><a class="dropdown-item" href="#" onclick="changeStatus({{ message.id }}, 'archived')">
                                            <i class="fas fa-circle text-secondary me-2"></i>مؤرشف
                                        </a></li>
                                        {% endif %}
                                    </ul>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>{{ message.created_at.strftime('%Y-%m-%d') }}</div>
                            <small class="text-muted">{{ message.created_at.strftime('%H:%M') }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('view_message', id=message.id) }}" class="btn btn-outline-primary btn-sm" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if message.attachments.count() > 0 %}
                                <button class="btn btn-outline-info btn-sm" title="مرفقات">
                                    <i class="fas fa-paperclip"></i>
                                </button>
                                {% endif %}
                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown" title="المزيد">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{{ url_for('view_message', id=message.id) }}">
                                        <i class="fas fa-eye me-2"></i>عرض
                                    </a></li>
                                    <li><a class="dropdown-item" href="{{ url_for('reply_message', id=message.id) }}">
                                        <i class="fas fa-reply me-2"></i>رد
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="forwardMessage({{ message.id }})">
                                        <i class="fas fa-share me-2"></i>إعادة توجيه
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li class="dropdown-submenu">
                                        <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">
                                            <i class="fas fa-flag me-2"></i>تغيير الحالة
                                        </a>
                                        <ul class="dropdown-menu dropdown-submenu-left">
                                            <li><a class="dropdown-item" href="#" onclick="changeStatus({{ message.id }}, 'new')">
                                                <i class="fas fa-circle text-success me-2"></i>جديد
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="changeStatus({{ message.id }}, 'read')">
                                                <i class="fas fa-circle text-info me-2"></i>مقروء
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="changeStatus({{ message.id }}, 'replied')">
                                                <i class="fas fa-circle text-warning me-2"></i>تم الرد
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="changeStatus({{ message.id }}, 'archived')">
                                                <i class="fas fa-circle text-secondary me-2"></i>مؤرشف
                                            </a></li>
                                        </ul>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="archiveMessage({{ message.id }})">
                                        <i class="fas fa-archive me-2"></i>أرشفة
                                    </a></li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Pagination -->
    {% if messages.pages > 1 %}
    <div class="card-footer">
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center mb-0">
                {% if messages.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('inbox', page=messages.prev_num) }}">السابق</a>
                    </li>
                {% endif %}
                
                {% for page_num in messages.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != messages.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('inbox', page=page_num) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if messages.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('inbox', page=messages.next_num) }}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        
        <div class="text-center mt-2">
            <small class="text-muted">
                عرض {{ messages.per_page * (messages.page - 1) + 1 }} - 
                {{ messages.per_page * (messages.page - 1) + messages.items|length }} 
                من {{ messages.total }} رسالة
            </small>
        </div>
    </div>
    {% endif %}
</div>

<!-- Bulk Actions -->
<div class="card mt-3" id="bulkActions" style="display: none;">
    <div class="card-body">
        <div class="d-flex align-items-center">
            <span class="me-3">
                <strong id="selectedCount">0</strong> رسالة محددة
            </span>
            <div class="btn-group">
                <button class="btn btn-outline-primary btn-sm" onclick="markAsRead()">
                    <i class="fas fa-eye me-1"></i>تحديد كمقروء
                </button>
                <button class="btn btn-outline-warning btn-sm" onclick="archiveSelected()">
                    <i class="fas fa-archive me-1"></i>أرشفة
                </button>
                <button class="btn btn-outline-danger btn-sm" onclick="deleteSelected()">
                    <i class="fas fa-trash me-1"></i>حذف
                </button>
            </div>
        </div>
    </div>
</div>

{% else %}
<div class="text-center py-5">
    <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
    <h4 class="text-muted">لا توجد رسائل واردة</h4>
    <p class="text-muted">ستظهر هنا الرسائل التي تستلمها من المستخدمين الآخرين</p>
    <a href="{{ url_for('compose') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>
        إنشاء رسالة جديدة
    </a>
</div>
{% endif %}

<style>
    .avatar-sm {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }
    
    .message-row:hover {
        background-color: rgba(0,123,255,0.1) !important;
    }
    
    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }

    /* Dropdown submenu styles */
    .dropdown-submenu {
        position: relative;
    }

    .dropdown-submenu .dropdown-menu {
        top: 0;
        left: 100%;
        margin-top: -1px;
        border-radius: 0.375rem;
    }

    .dropdown-submenu:hover .dropdown-menu {
        display: block;
    }

    .dropdown-submenu-left .dropdown-menu {
        left: -100%;
    }
</style>

<script>
// Filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('input[name="filter"]');
    const messageRows = document.querySelectorAll('.message-row');
    
    filterButtons.forEach(button => {
        button.addEventListener('change', function() {
            const filter = this.id;
            
            messageRows.forEach(row => {
                const classification = row.dataset.classification;
                const status = row.dataset.status;
                
                let show = true;
                
                if (filter === 'new' && status !== 'new') {
                    show = false;
                } else if (filter === 'urgent' && classification !== 'urgent') {
                    show = false;
                }
                
                row.style.display = show ? '' : 'none';
            });
        });
    });
    
    // Checkbox functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const messageCheckboxes = document.querySelectorAll('.message-checkbox');
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');
    
    selectAllCheckbox.addEventListener('change', function() {
        messageCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });
    
    messageCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });
    
    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.message-checkbox:checked');
        const count = checkedBoxes.length;
        
        if (count > 0) {
            bulkActions.style.display = 'block';
            selectedCount.textContent = count;
        } else {
            bulkActions.style.display = 'none';
        }
    }
});

// Action functions
function replyToMessage(messageId) {
    window.location.href = `/message/${messageId}#reply`;
}

function forwardMessage(messageId) {
    // Implementation for forwarding
    alert('ميزة إعادة التوجيه قيد التطوير');
}

function archiveMessage(messageId) {
    if (confirm('هل أنت متأكد من أرشفة هذه الرسالة؟')) {
        // Implementation for archiving
        alert('تم أرشفة الرسالة');
    }
}

function markAsRead() {
    const checkedBoxes = document.querySelectorAll('.message-checkbox:checked');
    if (checkedBoxes.length > 0) {
        alert(`تم تحديد ${checkedBoxes.length} رسالة كمقروءة`);
    }
}

function archiveSelected() {
    const checkedBoxes = document.querySelectorAll('.message-checkbox:checked');
    if (checkedBoxes.length > 0 && confirm(`هل أنت متأكد من أرشفة ${checkedBoxes.length} رسالة؟`)) {
        alert(`تم أرشفة ${checkedBoxes.length} رسالة`);
    }
}

function deleteSelected() {
    const checkedBoxes = document.querySelectorAll('.message-checkbox:checked');
    if (checkedBoxes.length > 0 && confirm(`هل أنت متأكد من حذف ${checkedBoxes.length} رسالة؟`)) {
        alert(`تم حذف ${checkedBoxes.length} رسالة`);
    }
}

function changeStatus(messageId, newStatus) {
    const statusNames = {
        'new': 'جديد',
        'read': 'مقروء',
        'replied': 'تم الرد',
        'archived': 'مؤرشف'
    };

    if (confirm(`هل أنت متأكد من تغيير حالة الرسالة إلى: ${statusNames[newStatus]}؟`)) {
        // إنشاء نموذج مخفي لإرسال الطلب
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/message/${messageId}/change_status`;

        // إضافة CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        // إضافة الحالة الجديدة
        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status';
        statusInput.value = newStatus;
        form.appendChild(statusInput);

        // إضافة النموذج للصفحة وإرساله
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
