{% extends "base.html" %}

{% block title %}الملف الشخصي - Profile{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user me-2"></i>
        الملف الشخصي
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('edit_profile') }}" class="btn btn-primary">
                <i class="fas fa-edit me-1"></i>
                تعديل الملف الشخصي
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- معلومات المستخدم -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-edit me-2"></i>
                    معلومات المستخدم
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label"><strong>اسم المستخدم:</strong></label>
                        <p class="form-control-plaintext">{{ current_user.username }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label"><strong>البريد الإلكتروني:</strong></label>
                        <p class="form-control-plaintext">{{ current_user.email }}</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label"><strong>الاسم الكامل:</strong></label>
                        <p class="form-control-plaintext">{{ current_user.full_name }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label"><strong>القسم:</strong></label>
                        <p class="form-control-plaintext">{{ current_user.department or 'غير محدد' }}</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label"><strong>الدور:</strong></label>
                        <p class="form-control-plaintext">
                            {% if current_user.role.value == 'admin' %}
                                <span class="badge bg-danger">مدير</span>
                            {% elif current_user.role.value == 'secretary' %}
                                <span class="badge bg-warning">سكرتير</span>
                            {% else %}
                                <span class="badge bg-primary">موظف</span>
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label"><strong>الحالة:</strong></label>
                        <p class="form-control-plaintext">
                            {% if current_user.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label"><strong>تاريخ التسجيل:</strong></label>
                        <p class="form-control-plaintext">{{ current_user.created_at.strftime('%Y-%m-%d') }}</p>
                    </div>
                    {% if current_user.last_login %}
                    <div class="col-md-6 mb-3">
                        <label class="form-label"><strong>آخر تسجيل دخول:</strong></label>
                        <p class="form-control-plaintext">{{ current_user.last_login.strftime('%Y-%m-%d %H:%M') }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- نشاط حديث -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    النشاط الحديث
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center py-3">
                    <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">لا يوجد نشاط حديث</p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- إعدادات الحساب -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    إعدادات الحساب
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('edit_profile') }}" class="btn btn-outline-primary">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الملف الشخصي
                    </a>
                    <a href="{{ url_for('change_password') }}" class="btn btn-outline-warning">
                        <i class="fas fa-key me-2"></i>
                        تغيير كلمة المرور
                    </a>
                    <button class="btn btn-outline-info" disabled>
                        <i class="fas fa-bell me-2"></i>
                        إعدادات الإشعارات
                        <small class="d-block">قريباً</small>
                    </button>
                </div>
            </div>
        </div>

        <!-- إحصائياتي -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائياتي
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <h4 class="text-primary">{{ current_user.sent_messages.count() }}</h4>
                        <small class="text-muted">رسائل مرسلة</small>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success">{{ current_user.received_messages.count() }}</h4>
                        <small class="text-muted">رسائل مستلمة</small>
                    </div>
                </div>

                <hr>

                <div class="row text-center">
                    <div class="col-6 mb-2">
                        <h5 class="text-warning">{{ current_user.received_messages.filter_by(status='new').count() }}</h5>
                        <small class="text-muted">رسائل جديدة</small>
                    </div>
                    <div class="col-6 mb-2">
                        <h5 class="text-info">{{ current_user.received_messages.filter_by(classification='urgent').count() }}</h5>
                        <small class="text-muted">رسائل عاجلة</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- صلاحياتي -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    صلاحياتي
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        إرسال الرسائل
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        استقبال الرسائل
                    </li>
                    {% if current_user.has_permission('archive') %}
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        أرشفة الرسائل
                    </li>
                    {% endif %}
                    {% if current_user.has_permission('view_all') %}
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        عرض جميع الرسائل
                    </li>
                    {% endif %}
                    {% if current_user.has_permission('manage_users') %}
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        إدارة المستخدمين
                    </li>
                    {% endif %}
                    {% if current_user.has_permission('reports') %}
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        عرض التقارير
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
