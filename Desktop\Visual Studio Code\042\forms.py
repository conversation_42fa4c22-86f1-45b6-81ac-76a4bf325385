from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON><PERSON>ield, FileAllowed, FileRequired
from wtforms import StringField, TextAreaField, SelectField, PasswordField, BooleanField, SubmitField, HiddenField
from wtforms.validators import DataRequired, Length, Email, EqualTo, Optional, ValidationError
from models import UserRole, MessageClassification, User

class LoginForm(FlaskForm):
    username = <PERSON><PERSON><PERSON>('اسم المستخدم / Username', validators=[DataRequired(), Length(min=3, max=80)])
    password = PasswordField('كلمة المرور / Password', validators=[DataRequired()])
    remember_me = BooleanField('تذكرني / Remember Me')
    submit = SubmitField('تسجيل الدخول / Login')

class MessageForm(FlaskForm):
    recipient_id = SelectField('المستلم / Recipient', coerce=int, validators=[DataRequired()])
    subject = StringField('الموضوع / Subject', validators=[DataRequired(), Length(min=5, max=200)])
    content = TextAreaField('المحتوى / Content', validators=[Optional(), Length(max=5000)])
    classification = SelectField('التصنيف / Classification', 
                                choices=[(MessageClassification.NORMAL.value, 'عادي / Normal'),
                                        (MessageClassification.SECRET.value, 'سري / Secret'),
                                        (MessageClassification.URGENT.value, 'عاجل / Urgent')],
                                default=MessageClassification.NORMAL.value)
    attachments = FileField('المرفقات / Attachments', 
                           validators=[FileAllowed(['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'],
                                                 'PDF, Word documents and images only!')])
    submit = SubmitField('إرسال / Send')


class UserForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=80)])
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()])
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(min=2, max=100)])
    department = StringField('القسم', validators=[Optional(), Length(max=100)])
    role = SelectField('الدور', choices=[
        ('admin', 'مدير'),
        ('secretary', 'سكرتير'),
        ('employee', 'موظف')
    ], validators=[DataRequired()])
    password = PasswordField('كلمة المرور', validators=[DataRequired(), Length(min=6)])
    password2 = PasswordField('تأكيد كلمة المرور', validators=[
        DataRequired(), EqualTo('password', message='كلمات المرور غير متطابقة')
    ])
    is_active = BooleanField('حساب نشط', default=True)
    submit = SubmitField('إضافة المستخدم')


class EditUserForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=80)])
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()])
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(min=2, max=100)])
    department = StringField('القسم', validators=[Optional(), Length(max=100)])
    role = SelectField('الدور', choices=[
        ('admin', 'مدير'),
        ('secretary', 'سكرتير'),
        ('employee', 'موظف')
    ], validators=[DataRequired()])
    is_active = BooleanField('حساب نشط', default=True)
    submit = SubmitField('حفظ التغييرات')


class ChangePasswordForm(FlaskForm):
    current_password = PasswordField('كلمة المرور الحالية', validators=[DataRequired()])
    new_password = PasswordField('كلمة المرور الجديدة', validators=[DataRequired(), Length(min=6)])
    new_password2 = PasswordField('تأكيد كلمة المرور الجديدة', validators=[
        DataRequired(), EqualTo('new_password', message='كلمات المرور غير متطابقة')
    ])
    submit = SubmitField('تغيير كلمة المرور')

class ReplyForm(FlaskForm):
    content = TextAreaField('الرد / Reply', validators=[DataRequired(), Length(min=10, max=2000)])
    submit = SubmitField('إرسال الرد / Send Reply')

class SearchForm(FlaskForm):
    query = StringField('البحث / Search', validators=[Optional(), Length(max=200)])
    message_number = StringField('رقم الرسالة / Message Number', validators=[Optional(), Length(max=50)])
    classification = SelectField('التصنيف / Classification', 
                                choices=[('', 'الكل / All'),
                                        (MessageClassification.NORMAL.value, 'عادي / Normal'),
                                        (MessageClassification.SECRET.value, 'سري / Secret'),
                                        (MessageClassification.URGENT.value, 'عاجل / Urgent')],
                                default='')
    date_from = StringField('من تاريخ / From Date', validators=[Optional()])
    date_to = StringField('إلى تاريخ / To Date', validators=[Optional()])
    submit = SubmitField('بحث / Search')

class UserForm(FlaskForm):
    username = StringField('اسم المستخدم / Username', validators=[DataRequired(), Length(min=3, max=80)])
    email = StringField('البريد الإلكتروني / Email', validators=[DataRequired(), Email()])
    full_name = StringField('الاسم الكامل / Full Name', validators=[DataRequired(), Length(min=2, max=200)])
    department = StringField('القسم / Department', validators=[Optional(), Length(max=100)])
    role = SelectField('الدور / Role', 
                      choices=[(UserRole.EMPLOYEE.value, 'موظف / Employee'),
                              (UserRole.SECRETARY.value, 'سكرتير / Secretary'),
                              (UserRole.ADMIN.value, 'مدير / Administrator')],
                      default=UserRole.EMPLOYEE.value)
    password = PasswordField('كلمة المرور / Password', validators=[DataRequired(), Length(min=6)])
    password2 = PasswordField('تأكيد كلمة المرور / Confirm Password', 
                             validators=[DataRequired(), EqualTo('password')])
    is_active = BooleanField('نشط / Active', default=True)
    submit = SubmitField('حفظ / Save')

class EditUserForm(FlaskForm):
    username = StringField('اسم المستخدم / Username', validators=[DataRequired(), Length(min=3, max=80)])
    email = StringField('البريد الإلكتروني / Email', validators=[DataRequired(), Email()])
    full_name = StringField('الاسم الكامل / Full Name', validators=[DataRequired(), Length(min=2, max=200)])
    department = StringField('القسم / Department', validators=[Optional(), Length(max=100)])
    role = SelectField('الدور / Role', 
                      choices=[(UserRole.EMPLOYEE.value, 'موظف / Employee'),
                              (UserRole.SECRETARY.value, 'سكرتير / Secretary'),
                              (UserRole.ADMIN.value, 'مدير / Administrator')],
                      default=UserRole.EMPLOYEE.value)
    is_active = BooleanField('نشط / Active', default=True)
    submit = SubmitField('تحديث / Update')

class ChangePasswordForm(FlaskForm):
    current_password = PasswordField('كلمة المرور الحالية / Current Password', validators=[DataRequired()])
    new_password = PasswordField('كلمة المرور الجديدة / New Password', validators=[DataRequired(), Length(min=6)])
    new_password2 = PasswordField('تأكيد كلمة المرور الجديدة / Confirm New Password', 
                                 validators=[DataRequired(), EqualTo('new_password')])
    submit = SubmitField('تغيير كلمة المرور / Change Password')
