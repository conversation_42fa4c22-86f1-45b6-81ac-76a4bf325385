{% extends "base.html" %}

{% block title %}عرض الرسالة - View Message{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-envelope-open me-2"></i>
        عرض الرسالة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {% if message.recipient_id == current_user.id %}
            <a href="{{ url_for('inbox') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للواردة
            </a>
            {% else %}
            <a href="{{ url_for('outbox') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للصادرة
            </a>
            {% endif %}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- معلومات الرسالة -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        تفاصيل الرسالة
                    </h5>
                    <div>
                        {% if message.classification.value == 'urgent' %}
                            <span class="badge bg-danger">عاجل</span>
                        {% elif message.classification.value == 'secret' %}
                            <span class="badge bg-warning">سري</span>
                        {% else %}
                            <span class="badge bg-primary">عادي</span>
                        {% endif %}
                        
                        {% if message.status.value == 'new' %}
                            <span class="badge bg-success">جديد</span>
                        {% elif message.status.value == 'read' %}
                            <span class="badge bg-info">مقروء</span>
                        {% else %}
                            <span class="badge bg-secondary">مؤرشف</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>رقم الرسالة:</strong>
                        <p class="mb-0">{{ message.message_number }}</p>
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ الإرسال:</strong>
                        <p class="mb-0">{{ message.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>من:</strong>
                        <p class="mb-0">{{ message.sender.full_name }} ({{ message.sender.username }})</p>
                    </div>
                    <div class="col-md-6">
                        <strong>إلى:</strong>
                        <p class="mb-0">{{ message.recipient.full_name }} ({{ message.recipient.username }})</p>
                    </div>
                </div>
                
                <div class="mb-3">
                    <strong>الموضوع:</strong>
                    <h4 class="mt-2">{{ message.subject }}</h4>
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <strong>المحتوى:</strong>
                    <div class="mt-2 p-3 bg-light rounded" style="white-space: pre-wrap;">
                        {{ message.content }}
                    </div>
                </div>
                
                {% if message.attachments %}
                <div class="mb-3">
                    <strong>المرفقات:</strong>
                    <div class="mt-2">
                        {% for attachment in message.attachments %}
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-paperclip me-2"></i>
                            <a href="{{ url_for('download_attachment', id=attachment.id) }}" class="text-decoration-none">
                                {{ attachment.filename }}
                            </a>
                            <small class="text-muted ms-2">({{ attachment.file_size }} bytes)</small>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- الردود -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-comments me-2"></i>
                    الردود
                </h5>
            </div>
            <div class="card-body text-center">
                <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                <p class="text-muted">لا توجد ردود على هذه الرسالة</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- إجراءات -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    الإجراءات المتاحة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if message.recipient_id == current_user.id %}
                    <a href="{{ url_for('reply_message', id=message.id) }}" class="btn btn-primary">
                        <i class="fas fa-reply me-2"></i>
                        رد على الرسالة
                    </a>
                    {% endif %}
                    
                    <button class="btn btn-outline-info" onclick="printMessage()">
                        <i class="fas fa-print me-2"></i>
                        طباعة الرسالة
                    </button>
                    
                    {% if message.sender_id == current_user.id or message.recipient_id == current_user.id %}
                    <button class="btn btn-outline-danger" onclick="confirmDelete()">
                        <i class="fas fa-trash me-2"></i>
                        حذف الرسالة
                    </button>
                    {% endif %}
                    
                    {% if current_user.has_permission('archive') %}
                    <button class="btn btn-outline-warning">
                        <i class="fas fa-archive me-2"></i>
                        أرشفة الرسالة
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info me-2"></i>
                    معلومات إضافية
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>حالة الرسالة:</strong></td>
                        <td>
                            {% if message.status.value == 'new' %}
                                <span class="text-success">جديد</span>
                            {% elif message.status.value == 'read' %}
                                <span class="text-info">مقروء</span>
                            {% else %}
                                <span class="text-secondary">مؤرشف</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>التصنيف:</strong></td>
                        <td>
                            {% if message.classification.value == 'urgent' %}
                                <span class="text-danger">عاجل</span>
                            {% elif message.classification.value == 'secret' %}
                                <span class="text-warning">سري</span>
                            {% else %}
                                <span class="text-primary">عادي</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% if message.reply_to %}
                    <tr>
                        <td><strong>رد على:</strong></td>
                        <td>
                            <a href="{{ url_for('view_message', id=message.reply_to.id) }}">
                                {{ message.reply_to.subject }}
                            </a>
                        </td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td><strong>عدد الردود:</strong></td>
                        <td>0</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal for delete confirmation -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذه الرسالة؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    هذا الإجراء لا يمكن التراجع عنه!
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" action="{{ url_for('delete_message', id=message.id) }}" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete() {
    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

function printMessage() {
    window.print();
}
</script>

<style>
@media print {
    .btn-toolbar, .card:not(:first-child), .col-lg-4 {
        display: none !important;
    }
    
    .col-lg-8 {
        width: 100% !important;
    }
}
</style>
{% endblock %}
