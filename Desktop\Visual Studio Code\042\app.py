import os
import re
from flask import Flask, render_template, redirect, url_for, flash, request
from flask_login import <PERSON>ginMana<PERSON>, login_user, logout_user, login_required, current_user
from datetime import datetime

from config import config
from models import db, User, Message, UserRole, MessageClassification, MessageStatus
from forms import LoginForm, MessageForm, UserForm, EditUserForm, ChangePasswordForm

def create_app(config_name=None):
    app = Flask(__name__)

    if config_name is None:
        config_name = os.environ.get('FLASK_CONFIG', 'default')

    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    # Initialize extensions
    db.init_app(app)

    # Initialize Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
    login_manager.login_message_category = 'info'

    # Add custom filters
    @app.template_filter('nl2br')
    def nl2br_filter(text):
        """Convert newlines to HTML line breaks"""
        if text is None:
            return ''
        return re.sub(r'\n', '<br>', str(text))

    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))
    
    # Routes
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return redirect(url_for('login'))

    @app.route('/login', methods=['GET', 'POST'])
    def login():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))

        form = LoginForm()
        if form.validate_on_submit():
            user = User.query.filter_by(username=form.username.data).first()
            if user and user.check_password(form.password.data) and user.is_active:
                login_user(user, remember=form.remember_me.data)
                user.last_login = datetime.utcnow()
                db.session.commit()

                next_page = request.args.get('next')
                if not next_page or not next_page.startswith('/'):
                    next_page = url_for('dashboard')
                return redirect(next_page)
            else:
                flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger')

        return render_template('auth/login.html', form=form)

    @app.route('/logout')
    @login_required
    def logout():
        logout_user()
        flash('تم تسجيل الخروج بنجاح', 'success')
        return redirect(url_for('login'))

    @app.route('/dashboard')
    @login_required
    def dashboard():
        # Get statistics
        total_messages = Message.query.count()
        new_messages = Message.query.filter_by(recipient_id=current_user.id, status=MessageStatus.NEW).count()
        urgent_messages = Message.query.filter_by(
            recipient_id=current_user.id,
            classification=MessageClassification.URGENT,
            status=MessageStatus.NEW
        ).count()

        # Get recent messages
        recent_messages = Message.query.filter_by(recipient_id=current_user.id)\
                                     .order_by(Message.created_at.desc())\
                                     .limit(5).all()

        return render_template('dashboard.html',
                             total_messages=total_messages,
                             new_messages=new_messages,
                             urgent_messages=urgent_messages,
                             recent_messages=recent_messages)

    @app.route('/compose', methods=['GET', 'POST'])
    @login_required
    def compose():
        form = MessageForm()

        # Populate recipient choices
        users = User.query.filter(User.id != current_user.id, User.is_active == True).all()
        form.recipient_id.choices = [(user.id, f"{user.full_name} ({user.department or 'N/A'})") for user in users]

        if form.validate_on_submit():
            # Generate message number
            today = datetime.now().strftime('%Y%m%d')
            count = Message.query.filter(Message.message_number.like(f'{today}%')).count()
            message_number = f'{today}-{count + 1:04d}'

            message = Message(
                message_number=message_number,
                subject=form.subject.data,
                content=form.content.data,
                classification=MessageClassification(form.classification.data),
                sender_id=current_user.id,
                recipient_id=form.recipient_id.data
            )

            db.session.add(message)
            db.session.commit()

            flash('تم إرسال الرسالة بنجاح', 'success')
            return redirect(url_for('outbox'))

        return render_template('messages/compose.html', form=form)

    @app.route('/inbox')
    @login_required
    def inbox():
        page = request.args.get('page', 1, type=int)
        messages = Message.query.filter_by(recipient_id=current_user.id)\
                               .order_by(Message.created_at.desc())\
                               .paginate(page=page, per_page=20, error_out=False)

        return render_template('messages/inbox.html', messages=messages)

    @app.route('/outbox')
    @login_required
    def outbox():
        page = request.args.get('page', 1, type=int)
        messages = Message.query.filter_by(sender_id=current_user.id)\
                               .order_by(Message.created_at.desc())\
                               .paginate(page=page, per_page=20, error_out=False)

        return render_template('messages/outbox.html', messages=messages)

    # Add missing routes
    @app.route('/search')
    @login_required
    def search():
        return render_template('search.html')

    @app.route('/archive')
    @login_required
    def archive():
        return render_template('archive.html')

    @app.route('/reports')
    @login_required
    def reports():
        return render_template('reports.html')

    @app.route('/users')
    @login_required
    def users():
        if not current_user.has_permission('manage_users'):
            flash('ليس لديك صلاحية لإدارة المستخدمين', 'danger')
            return redirect(url_for('dashboard'))

        page = request.args.get('page', 1, type=int)
        users = User.query.order_by(User.created_at.desc())\
                         .paginate(page=page, per_page=20, error_out=False)

        # إحصائيات المستخدمين
        total_users = User.query.count()
        active_users = User.query.filter_by(is_active=True).count()
        admin_users = User.query.filter_by(role=UserRole.ADMIN).count()
        employee_users = User.query.filter_by(role=UserRole.EMPLOYEE).count()

        return render_template('users.html',
                             users=users,
                             total_users=total_users,
                             active_users=active_users,
                             admin_users=admin_users,
                             employee_users=employee_users)

    @app.route('/users/add', methods=['GET', 'POST'])
    @login_required
    def add_user():
        if not current_user.has_permission('manage_users'):
            flash('ليس لديك صلاحية لإدارة المستخدمين', 'danger')
            return redirect(url_for('dashboard'))

        form = UserForm()
        if form.validate_on_submit():
            # التحقق من عدم وجود اسم المستخدم أو البريد الإلكتروني
            existing_user = User.query.filter(
                (User.username == form.username.data) |
                (User.email == form.email.data)
            ).first()

            if existing_user:
                flash('اسم المستخدم أو البريد الإلكتروني موجود بالفعل', 'danger')
            else:
                user = User(
                    username=form.username.data,
                    email=form.email.data,
                    full_name=form.full_name.data,
                    department=form.department.data,
                    role=UserRole(form.role.data),
                    is_active=form.is_active.data
                )
                user.set_password(form.password.data)

                db.session.add(user)
                db.session.commit()

                flash(f'تم إنشاء المستخدم {user.full_name} بنجاح', 'success')
                return redirect(url_for('users'))

        return render_template('add_user.html', form=form)

    @app.route('/users/<int:id>/edit', methods=['GET', 'POST'])
    @login_required
    def edit_user(id):
        if not current_user.has_permission('manage_users'):
            flash('ليس لديك صلاحية لإدارة المستخدمين', 'danger')
            return redirect(url_for('dashboard'))

        user = User.query.get_or_404(id)
        form = EditUserForm(obj=user)

        if form.validate_on_submit():
            # التحقق من عدم تكرار اسم المستخدم أو البريد الإلكتروني
            existing_user = User.query.filter(
                (User.username == form.username.data) |
                (User.email == form.email.data),
                User.id != id
            ).first()

            if existing_user:
                flash('اسم المستخدم أو البريد الإلكتروني موجود بالفعل', 'danger')
            else:
                user.username = form.username.data
                user.email = form.email.data
                user.full_name = form.full_name.data
                user.department = form.department.data
                user.role = UserRole(form.role.data)
                user.is_active = form.is_active.data

                db.session.commit()

                flash(f'تم تحديث بيانات {user.full_name} بنجاح', 'success')
                return redirect(url_for('users'))

        return render_template('edit_user.html', form=form, user=user)

    @app.route('/users/<int:id>/delete', methods=['POST'])
    @login_required
    def delete_user(id):
        if not current_user.has_permission('manage_users'):
            flash('ليس لديك صلاحية لإدارة المستخدمين', 'danger')
            return redirect(url_for('dashboard'))

        user = User.query.get_or_404(id)

        # منع حذف المدير الحالي
        if user.id == current_user.id:
            flash('لا يمكنك حذف حسابك الخاص', 'danger')
            return redirect(url_for('users'))

        # منع حذف المدير الوحيد
        if user.role == UserRole.ADMIN:
            admin_count = User.query.filter_by(role=UserRole.ADMIN, is_active=True).count()
            if admin_count <= 1:
                flash('لا يمكن حذف المدير الوحيد في النظام', 'danger')
                return redirect(url_for('users'))

        username = user.full_name
        db.session.delete(user)
        db.session.commit()

        flash(f'تم حذف المستخدم {username} بنجاح', 'success')
        return redirect(url_for('users'))

    @app.route('/profile')
    @login_required
    def profile():
        return render_template('profile.html')

    @app.route('/profile/edit', methods=['GET', 'POST'])
    @login_required
    def edit_profile():
        form = EditUserForm(obj=current_user)
        # إزالة حقل الدور من النموذج للمستخدم العادي
        if not current_user.has_permission('manage_users'):
            del form.role
            del form.is_active

        if form.validate_on_submit():
            # التحقق من عدم تكرار اسم المستخدم أو البريد الإلكتروني
            existing_user = User.query.filter(
                (User.username == form.username.data) |
                (User.email == form.email.data),
                User.id != current_user.id
            ).first()

            if existing_user:
                flash('اسم المستخدم أو البريد الإلكتروني موجود بالفعل', 'danger')
            else:
                current_user.username = form.username.data
                current_user.email = form.email.data
                current_user.full_name = form.full_name.data
                current_user.department = form.department.data

                # السماح للمدير بتغيير الدور والحالة
                if current_user.has_permission('manage_users'):
                    current_user.role = UserRole(form.role.data)
                    current_user.is_active = form.is_active.data

                db.session.commit()

                flash('تم تحديث الملف الشخصي بنجاح', 'success')
                return redirect(url_for('profile'))

        return render_template('edit_profile.html', form=form)

    @app.route('/change_password', methods=['GET', 'POST'])
    @login_required
    def change_password():
        form = ChangePasswordForm()

        if form.validate_on_submit():
            if current_user.check_password(form.current_password.data):
                current_user.set_password(form.new_password.data)
                db.session.commit()

                flash('تم تغيير كلمة المرور بنجاح', 'success')
                return redirect(url_for('profile'))
            else:
                flash('كلمة المرور الحالية غير صحيحة', 'danger')

        return render_template('change_password.html', form=form)

    @app.route('/message/<int:id>')
    @login_required
    def view_message(id):
        message = Message.query.get_or_404(id)

        # التحقق من صلاحية عرض الرسالة
        if not (message.sender_id == current_user.id or
                message.recipient_id == current_user.id or
                current_user.has_permission('view_all')):
            flash('ليس لديك صلاحية لعرض هذه الرسالة', 'danger')
            return redirect(url_for('dashboard'))

        # تحديث حالة الرسالة إلى مقروءة إذا كان المستلم
        if message.recipient_id == current_user.id and message.status == MessageStatus.NEW:
            message.status = MessageStatus.READ
            db.session.commit()

        return render_template('view_message.html', message=message)

    @app.route('/message/<int:id>/reply', methods=['GET', 'POST'])
    @login_required
    def reply_message(id):
        original_message = Message.query.get_or_404(id)

        # التحقق من صلاحية الرد
        if original_message.recipient_id != current_user.id:
            flash('يمكنك الرد فقط على الرسائل المرسلة إليك', 'danger')
            return redirect(url_for('dashboard'))

        form = MessageForm()

        if form.validate_on_submit():
            # إنشاء رقم رسالة جديد
            today = datetime.now().strftime('%Y%m%d')
            last_message = Message.query.filter(Message.message_number.like(f'{today}-%')).order_by(Message.id.desc()).first()

            if last_message:
                last_number = int(last_message.message_number.split('-')[1])
                new_number = f"{today}-{last_number + 1:04d}"
            else:
                new_number = f"{today}-0001"

            reply = Message(
                message_number=new_number,
                subject=f"رد: {original_message.subject}",
                content=form.content.data,
                sender_id=current_user.id,
                recipient_id=original_message.sender_id,
                classification=MessageClassification(form.classification.data),
                status=MessageStatus.NEW,
                reply_to_id=original_message.id
            )

            db.session.add(reply)
            db.session.commit()

            flash('تم إرسال الرد بنجاح', 'success')
            return redirect(url_for('view_message', id=original_message.id))

        # تعبئة النموذج بالبيانات الافتراضية
        form.recipient_id.data = original_message.sender_id
        form.subject.data = f"رد: {original_message.subject}"

        return render_template('reply_message.html', form=form, original_message=original_message)

    @app.route('/message/<int:id>/delete', methods=['POST'])
    @login_required
    def delete_message(id):
        message = Message.query.get_or_404(id)

        # التحقق من صلاحية الحذف
        if not (message.sender_id == current_user.id or
                message.recipient_id == current_user.id or
                current_user.has_permission('manage_all')):
            flash('ليس لديك صلاحية لحذف هذه الرسالة', 'danger')
            return redirect(url_for('dashboard'))

        subject = message.subject
        db.session.delete(message)
        db.session.commit()

        flash(f'تم حذف الرسالة "{subject}" بنجاح', 'success')
        return redirect(url_for('inbox'))

    # Initialize database
    with app.app_context():
        db.create_all()

        # Create default admin user if not exists
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='System Administrator',
                role=UserRole.ADMIN
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("Default admin user created: username='admin', password='admin123'")

    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=3030)
