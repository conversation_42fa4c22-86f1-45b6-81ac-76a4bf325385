نظام المراسلات الداخلية - تعليمات التشغيل
==========================================

🚀 طريقة التشغيل:
1. تأكد من تثبيت Python 3.8 أو أحدث
2. افتح Command Prompt في مجلد المشروع
3. نفذ الأمر: pip install -r requirements.txt
4. نفذ الأمر: python app.py
   أو انقر مرتين على ملف run.bat

🌐 الوصول للنظام:
- افتح المتصفح على: http://localhost:5000
- أو: http://127.0.0.1:5000

🔐 بيانات الدخول الافتراضية:
- اسم المستخدم: admin
- كلمة المرور: admin123

📋 المميزات المتاحة:
✅ تسجيل الدخول والخروج
✅ لوحة التحكم مع الإحصائيات
✅ إنشاء رسائل جديدة
✅ عرض الرسائل الواردة والصادرة
✅ تصنيف الرسائل (عادي، سري، عاجل)
✅ البحث المتقدم في الرسائل
✅ إدارة المستخدمين (للمديرين)
✅ الملف الشخصي وتعديل البيانات
✅ تغيير كلمة المرور مع مؤشر القوة
✅ نظام صلاحيات متدرج
✅ واجهة عربية مع دعم RTL

🔧 المميزات قيد التطوير:
- إرفاق الملفات
- نظام الإشعارات
- التقارير المفصلة
- الأرشيف المتقدم

👥 إدارة المستخدمين:
- إضافة مستخدمين جدد
- تعديل بيانات المستخدمين
- حذف المستخدمين (مع حماية المدير)
- تفعيل/إلغاء تفعيل الحسابات
- إعادة تعيين كلمات المرور

🔐 الملف الشخصي:
- عرض البيانات الشخصية
- تعديل المعلومات
- تغيير كلمة المرور
- عرض النشاط الحديث
- عرض الصلاحيات

⚠️ ملاحظات مهمة:
1. غير كلمة مرور المدير فور التثبيت
2. النظام مصمم للاستخدام المحلي (LAN)
3. قم بعمل نسخة احتياطية من قاعدة البيانات بانتظام

📞 للدعم التقني:
- راجع ملف README.md للتفاصيل الكاملة
- تحقق من سجلات النظام في حالة وجود مشاكل

تم التطوير بواسطة: Augment Agent
التاريخ: ديسمبر 2024
