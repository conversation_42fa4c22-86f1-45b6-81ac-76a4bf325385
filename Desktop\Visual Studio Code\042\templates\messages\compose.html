{% extends "base.html" %}

{% block title %}إنشاء رسالة جديدة - Compose Message{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-edit me-2"></i>
        إنشاء رسالة جديدة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-envelope me-2"></i>
                    تفاصيل الرسالة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.recipient_id.label(class="form-label") }}
                            {{ form.recipient_id(class="form-select" + (" is-invalid" if form.recipient_id.errors else "")) }}
                            {% if form.recipient_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.recipient_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.classification.label(class="form-label") }}
                            {{ form.classification(class="form-select" + (" is-invalid" if form.classification.errors else "")) }}
                            {% if form.classification.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.classification.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.subject.label(class="form-label") }}
                        {{ form.subject(class="form-control" + (" is-invalid" if form.subject.errors else ""), placeholder="أدخل موضوع الرسالة") }}
                        {% if form.subject.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.subject.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.content.label(class="form-label") }}
                        {{ form.content(class="form-control" + (" is-invalid" if form.content.errors else ""), rows="8", placeholder="أدخل محتوى الرسالة...") }}
                        {% if form.content.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.content.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.attachments.label(class="form-label") }}
                        {{ form.attachments(class="form-control" + (" is-invalid" if form.attachments.errors else "")) }}
                        {% if form.attachments.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.attachments.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            الملفات المسموحة: PDF, Word, الصور (حد أقصى 16 ميجابايت)
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <div>
                            {{ form.submit(class="btn btn-primary btn-lg") }}
                            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary btn-lg ms-2">إلغاء</a>
                        </div>
                        <div class="text-muted small">
                            <i class="fas fa-info-circle me-1"></i>
                            سيتم إنشاء رقم الرسالة تلقائياً
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Help Card -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>
                    مساعدة
                </h6>
            </div>
            <div class="card-body">
                <h6>تصنيفات الرسائل:</h6>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <span class="badge badge-normal me-2">عادي</span>
                        للرسائل العادية
                    </li>
                    <li class="mb-2">
                        <span class="badge badge-secret me-2">سري</span>
                        للرسائل السرية
                    </li>
                    <li class="mb-2">
                        <span class="badge badge-urgent me-2">عاجل</span>
                        للرسائل العاجلة
                    </li>
                </ul>
                
                <hr>
                
                <h6>نصائح:</h6>
                <ul class="small">
                    <li>اختر عنواناً واضحاً ومفهوماً</li>
                    <li>استخدم التصنيف المناسب</li>
                    <li>تأكد من اختيار المستلم الصحيح</li>
                    <li>يمكنك إرفاق ملفات متعددة</li>
                </ul>
            </div>
        </div>
        
        <!-- Recent Recipients -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    المستلمون الحديثون
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <!-- This would be populated with recent recipients -->
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-user-friends fa-2x mb-2"></i>
                        <p class="small mb-0">سيظهر هنا المستلمون الذين راسلتهم مؤخراً</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .form-control:focus,
    .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
    }
    
    .btn-primary:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
</style>
{% endblock %}
