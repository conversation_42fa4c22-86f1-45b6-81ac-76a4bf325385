# نظام المراسلات الداخلية - Internal Messaging System

نظام شامل لإدارة المراسلات الداخلية في المؤسسات والشركات، مصمم خصيصاً للاستخدام الداخلي بين الموظفين والأقسام.

## المميزات الرئيسية

### 🔐 نظام تسجيل الدخول
- تسجيل دخول آمن بالمستخدم وكلمة المرور
- إدارة الجلسات
- ثلاثة مستويات صلاحيات: مدير، سكرتير، موظف
- تسجيل خروج آمن

### 📊 لوحة التحكم
- عرض إجمالي عدد الرسائل
- عرض الرسائل الجديدة وغير المقروءة
- إشعارات الرسائل العاجلة
- روابط سريعة للوظائف الأساسية

### 📝 إدارة المراسلات

#### الرسائل الصادرة:
- ترقيم تلقائي للرسائل
- تحديد الموضوع والمحتوى
- اختيار المستلم (قسم أو موظف)
- تصنيف الرسائل (عادي - سري - عاجل)
- إرفاق ملفات (PDF, Word, صور)
- حفظ وأرشفة الرسائل الصادرة

#### الرسائل الواردة:
- عرض المرسل والموضوع
- تصنيف الرسائل
- حالة القراءة (جديد/مقروء/تم الرد)
- الرد على الرسائل
- إعادة التوجيه
- أرشفة الرسائل الواردة

### 🔍 البحث المتقدم
- البحث برقم الرسالة
- البحث بالموضوع أو اسم الموظف
- فلترة حسب التاريخ والتصنيف والحالة
- عرض النتائج وطباعتها بصيغة PDF أو Excel

### 📁 أرشيف الرسائل
- أرشيف الرسائل الواردة والصادرة
- معاينة الرسائل المؤرشفة
- تحميل المرفقات
- طباعة الرسائل

### 👥 إدارة المستخدمين والصلاحيات
- إضافة/تعديل/حذف المستخدمين
- تحديد الصلاحيات (إرسال، أرشفة، عرض فقط، إدارة)
- تسجيل عمليات الدخول والخروج
- متابعة سجل التعديلات

### 🔔 الإشعارات والتنبيهات
- إشعار بوصول رسالة جديدة
- إشعار بالرسائل التي تحتاج رد
- إرسال تنبيهات داخلية عبر النظام

### 📈 التقارير
- تقارير يومية/شهرية للمراسلات
- تقارير حسب الجهة/الموظف
- إحصائيات بالأرقام (عدد الرسائل، الردود، الأرشيف)
- تصدير بصيغة PDF و Excel

## التقنيات المستخدمة

- **اللغة**: Python 3
- **إطار العمل**: Flask
- **قاعدة البيانات**: SQLite (قابل للترقية إلى PostgreSQL)
- **واجهة المستخدم**: HTML + Bootstrap RTL (دعم العربية)
- **رفع الملفات**: دعم PDF/Word/الصور
- **الأمان**: تشفير كلمات المرور + إدارة الصلاحيات

## متطلبات التشغيل

```bash
Python 3.8+
Flask >= 2.3.0
Flask-SQLAlchemy >= 3.0.0
Flask-Login >= 0.6.0
Flask-WTF >= 1.1.0
WTForms >= 3.0.0
```

## التثبيت والتشغيل

1. **تثبيت المتطلبات:**
```bash
pip install -r requirements.txt
```

2. **تشغيل التطبيق:**
```bash
python app.py
```

3. **الوصول للنظام:**
- افتح المتصفح على: `http://localhost:3030`
- بيانات المدير الافتراضية:
  - اسم المستخدم: `admin`
  - كلمة المرور: `admin123`

## الهيكل التقني

### قاعدة البيانات
- جدول المستخدمين (users)
- جدول الرسائل (messages)
- جدول المرفقات (attachments)
- جدول الردود (message_replies)
- جدول الإشعارات (notifications)
- جدول سجل الأنشطة (activity_logs)

### أدوار المستخدمين
- **المدير**: جميع الصلاحيات + إدارة المستخدمين + التقارير
- **السكرتير**: إرسال واستقبال + أرشفة + عرض القسم
- **الموظف**: إرسال واستقبال + عرض الرسائل الخاصة

## المميزات الأمنية

- تشفير كلمات المرور
- إدارة الجلسات الآمنة
- تسجيل جميع العمليات
- صلاحيات متدرجة
- حماية من رفع ملفات ضارة

## النشر

### النشر المحلي (LAN)
```bash
python app.py
# النظام سيعمل على جميع عناوين IP المحلية
```

### النشر على الخادم
```bash
# استخدام Gunicorn للإنتاج
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

## الدعم والصيانة

- النظام مصمم للعمل بدون إنترنت (LAN فقط)
- نسخ احتياطية تلقائية لقاعدة البيانات
- سجلات مفصلة لجميع العمليات
- واجهة سهلة الاستخدام باللغة العربية

## ملاحظات مهمة

1. **الأمان**: غير كلمة مرور المدير الافتراضية فور التثبيت
2. **النسخ الاحتياطية**: قم بعمل نسخة احتياطية من ملف قاعدة البيانات بانتظام
3. **الصيانة**: راجع سجلات النظام دورياً
4. **التحديثات**: تحقق من التحديثات الأمنية للمكتبات المستخدمة

## الترخيص

هذا النظام مطور للاستخدام الداخلي في المؤسسات والشركات.

---

**تم التطوير بواسطة**: Augment Agent  
**التاريخ**: ديسمبر 2024  
**الإصدار**: 1.0.0
