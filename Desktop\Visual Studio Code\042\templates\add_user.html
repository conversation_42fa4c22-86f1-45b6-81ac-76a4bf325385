{% extends "base.html" %}

{% block title %}إضافة مستخدم جديد - Add User{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-plus me-2"></i>
        إضافة مستخدم جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('users') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    بيانات المستخدم الجديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                            {% if form.username.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.full_name.label(class="form-label") }}
                            {{ form.full_name(class="form-control" + (" is-invalid" if form.full_name.errors else "")) }}
                            {% if form.full_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.full_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.department.label(class="form-label") }}
                            {{ form.department(class="form-control" + (" is-invalid" if form.department.errors else "")) }}
                            {% if form.department.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.department.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.role.label(class="form-label") }}
                            {{ form.role(class="form-select" + (" is-invalid" if form.role.errors else "")) }}
                            {% if form.role.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.role.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الحالة</label>
                            <div class="form-check">
                                {{ form.is_active(class="form-check-input") }}
                                {{ form.is_active.label(class="form-check-label") }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.password.label(class="form-label") }}
                            {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                            {% if form.password.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">كلمة المرور يجب أن تكون 6 أحرف على الأقل</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.password2.label(class="form-label") }}
                            {{ form.password2(class="form-control" + (" is-invalid" if form.password2.errors else "")) }}
                            {% if form.password2.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.password2.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <div>
                            {{ form.submit(class="btn btn-primary btn-lg") }}
                            <a href="{{ url_for('users') }}" class="btn btn-secondary btn-lg ms-2">إلغاء</a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- معلومات مساعدة -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مهمة
                </h6>
            </div>
            <div class="card-body">
                <h6>أدوار المستخدمين:</h6>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <span class="badge bg-danger me-2">مدير</span>
                        جميع الصلاحيات
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-warning me-2">سكرتير</span>
                        إرسال واستقبال + أرشفة
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-primary me-2">موظف</span>
                        إرسال واستقبال فقط
                    </li>
                </ul>
                
                <hr>
                
                <h6>نصائح:</h6>
                <ul class="small">
                    <li>اختر اسم مستخدم واضح ومفهوم</li>
                    <li>استخدم بريد إلكتروني صحيح</li>
                    <li>اختر كلمة مرور قوية</li>
                    <li>حدد القسم بوضوح</li>
                </ul>
            </div>
        </div>
        
        <!-- إحصائيات سريعة -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات المستخدمين
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-2">
                        <h5 class="text-primary">{{ total_users or 0 }}</h5>
                        <small class="text-muted">إجمالي المستخدمين</small>
                    </div>
                    <div class="col-6 mb-2">
                        <h5 class="text-success">{{ active_users or 0 }}</h5>
                        <small class="text-muted">مستخدمين نشطين</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .form-control:focus,
    .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
    }
    
    .btn-primary:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
</style>
{% endblock %}
