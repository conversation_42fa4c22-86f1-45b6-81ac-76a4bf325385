{% extends "base.html" %}

{% block title %}إدارة المستخدمين - User Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>
        إدارة المستخدمين
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('add_user') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة مستخدم جديد
            </a>
        </div>
    </div>
</div>

<!-- إحصائيات المستخدمين -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary">{{ total_users }}</h3>
                <p class="text-muted">إجمالي المستخدمين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success">{{ active_users }}</h3>
                <p class="text-muted">مستخدمين نشطين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning">{{ admin_users }}</h3>
                <p class="text-muted">مديرين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-info">{{ employee_users }}</h3>
                <p class="text-muted">موظفين</p>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة المستخدمين</h5>
    </div>
    <div class="card-body">
        {% if users.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>اسم المستخدم</th>
                        <th>الاسم الكامل</th>
                        <th>البريد الإلكتروني</th>
                        <th>القسم</th>
                        <th>الدور</th>
                        <th>الحالة</th>
                        <th>تاريخ التسجيل</th>
                        <th>آخر دخول</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users.items %}
                    <tr>
                        <td>
                            <strong>{{ user.username }}</strong>
                            {% if user.id == current_user.id %}
                                <span class="badge bg-info ms-1">أنت</span>
                            {% endif %}
                        </td>
                        <td>{{ user.full_name }}</td>
                        <td>{{ user.email }}</td>
                        <td>{{ user.department or '-' }}</td>
                        <td>
                            {% if user.role.value == 'admin' %}
                                <span class="badge bg-danger">مدير</span>
                            {% elif user.role.value == 'secretary' %}
                                <span class="badge bg-warning">سكرتير</span>
                            {% else %}
                                <span class="badge bg-primary">موظف</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>
                            {% if user.last_login %}
                                {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}
                            {% else %}
                                <span class="text-muted">لم يسجل دخول</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('edit_user', id=user.id) }}" class="btn btn-outline-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if user.id != current_user.id %}
                                <button class="btn btn-outline-danger" title="حذف"
                                        onclick="confirmDelete('{{ user.full_name }}', {{ user.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% else %}
                                <button class="btn btn-outline-secondary" title="لا يمكن حذف حسابك" disabled>
                                    <i class="fas fa-ban"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if users.pages > 1 %}
        <nav aria-label="Page navigation" class="mt-3">
            <ul class="pagination justify-content-center">
                {% if users.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('users', page=users.prev_num) }}">السابق</a>
                    </li>
                {% endif %}

                {% for page_num in users.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != users.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('users', page=page_num) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if users.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('users', page=users.next_num) }}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-users fa-4x text-muted mb-3"></i>
            <h4 class="text-muted">لا يوجد مستخدمين</h4>
            <p class="text-muted">ابدأ بإضافة مستخدمين جدد للنظام</p>
            <a href="{{ url_for('add_user') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة مستخدم جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal for delete confirmation -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المستخدم <strong id="deleteUserName"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    هذا الإجراء لا يمكن التراجع عنه!
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(userName, userId) {
    document.getElementById('deleteUserName').textContent = userName;
    document.getElementById('deleteForm').action = `/users/${userId}/delete`;

    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
{% endblock %}
