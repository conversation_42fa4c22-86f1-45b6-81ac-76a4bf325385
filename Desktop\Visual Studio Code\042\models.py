from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
import enum

db = SQLAlchemy()

class UserRole(enum.Enum):
    ADMIN = "admin"
    EMPLOYEE = "employee"
    SECRETARY = "secretary"

class MessageClassification(enum.Enum):
    NORMAL = "normal"
    SECRET = "secret"
    URGENT = "urgent"

class MessageStatus(enum.Enum):
    NEW = "new"
    READ = "read"
    REPLIED = "replied"
    ARCHIVED = "archived"

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(200), nullable=False)
    department = db.Column(db.String(100), nullable=True)
    role = db.Column(db.Enum(UserRole), nullable=False, default=UserRole.EMPLOYEE)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    # Relationships
    sent_messages = db.relationship('Message', foreign_keys='Message.sender_id', backref='sender', lazy='dynamic')
    received_messages = db.relationship('Message', foreign_keys='Message.recipient_id', backref='recipient', lazy='dynamic')
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def has_permission(self, permission):
        permissions = {
            UserRole.ADMIN: ['send', 'receive', 'archive', 'manage_users', 'view_all', 'reports'],
            UserRole.SECRETARY: ['send', 'receive', 'archive', 'view_department'],
            UserRole.EMPLOYEE: ['send', 'receive', 'view_own']
        }
        return permission in permissions.get(self.role, [])
    
    def __repr__(self):
        return f'<User {self.username}>'

class Message(db.Model):
    __tablename__ = 'messages'
    
    id = db.Column(db.Integer, primary_key=True)
    message_number = db.Column(db.String(50), unique=True, nullable=False)
    subject = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text, nullable=True)
    classification = db.Column(db.Enum(MessageClassification), nullable=False, default=MessageClassification.NORMAL)
    status = db.Column(db.Enum(MessageStatus), nullable=False, default=MessageStatus.NEW)
    
    # Foreign keys
    sender_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    recipient_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    read_at = db.Column(db.DateTime)
    replied_at = db.Column(db.DateTime)
    
    # Relationships
    attachments = db.relationship('Attachment', backref='message', lazy='dynamic', cascade='all, delete-orphan')
    replies = db.relationship('MessageReply', backref='original_message', lazy='dynamic', cascade='all, delete-orphan')
    
    def mark_as_read(self):
        if self.status == MessageStatus.NEW:
            self.status = MessageStatus.READ
            self.read_at = datetime.utcnow()
            db.session.commit()
    
    def generate_message_number(self):
        # Generate unique message number based on date and sequence
        today = datetime.now().strftime('%Y%m%d')
        count = Message.query.filter(Message.message_number.like(f'{today}%')).count()
        return f'{today}-{count + 1:04d}'
    
    def __repr__(self):
        return f'<Message {self.message_number}: {self.subject}>'

class Attachment(db.Model):
    __tablename__ = 'attachments'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer, nullable=False)
    mime_type = db.Column(db.String(100), nullable=False)
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Foreign key
    message_id = db.Column(db.Integer, db.ForeignKey('messages.id'), nullable=False)

    def __repr__(self):
        return f'<Attachment {self.original_filename}>'

class MessageReply(db.Model):
    __tablename__ = 'message_replies'

    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Foreign keys
    message_id = db.Column(db.Integer, db.ForeignKey('messages.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Relationships
    user = db.relationship('User', backref='replies')

    def __repr__(self):
        return f'<Reply to {self.message_id} by {self.user_id}>'

class Notification(db.Model):
    __tablename__ = 'notifications'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    notification_type = db.Column(db.String(50), nullable=False)  # 'new_message', 'urgent', 'reply_required'
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Foreign keys
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    message_id = db.Column(db.Integer, db.ForeignKey('messages.id'), nullable=True)

    # Relationships
    user = db.relationship('User', backref='notifications')
    related_message = db.relationship('Message', backref='notifications')

    def __repr__(self):
        return f'<Notification {self.title} for {self.user_id}>'

class ActivityLog(db.Model):
    __tablename__ = 'activity_logs'

    id = db.Column(db.Integer, primary_key=True)
    action = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=False)
    ip_address = db.Column(db.String(45), nullable=True)
    user_agent = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Foreign key
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Relationships
    user = db.relationship('User', backref='activity_logs')

    def __repr__(self):
        return f'<ActivityLog {self.action} by {self.user_id}>'
