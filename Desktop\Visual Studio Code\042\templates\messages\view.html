{% extends "base.html" %}

{% block title %}عرض الرسالة - View Message{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-envelope-open me-2"></i>
        عرض الرسالة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('inbox') if message.recipient_id == current_user.id else url_for('outbox') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة
            </a>
            {% if message.recipient_id == current_user.id %}
            <button class="btn btn-primary" onclick="showReplyForm()">
                <i class="fas fa-reply me-1"></i>
                رد
            </button>
            {% endif %}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Message Details -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ message.subject }}</h5>
                    <div>
                        {% if message.classification.value == 'urgent' %}
                            <span class="badge badge-urgent">عاجل</span>
                        {% elif message.classification.value == 'secret' %}
                            <span class="badge badge-secret">سري</span>
                        {% else %}
                            <span class="badge badge-normal">عادي</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Message Header -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>من:</strong> {{ message.sender.full_name }}<br>
                        <small class="text-muted">{{ message.sender.department or 'N/A' }}</small>
                    </div>
                    <div class="col-md-6">
                        <strong>إلى:</strong> {{ message.recipient.full_name }}<br>
                        <small class="text-muted">{{ message.recipient.department or 'N/A' }}</small>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>رقم الرسالة:</strong> {{ message.message_number }}
                    </div>
                    <div class="col-md-6">
                        <strong>التاريخ:</strong> {{ message.created_at.strftime('%Y-%m-%d %H:%M') }}
                    </div>
                </div>
                
                <hr>
                
                <!-- Message Content -->
                <div class="message-content">
                    {% if message.content %}
                        <p>{{ message.content|nl2br|safe }}</p>
                    {% else %}
                        <p class="text-muted fst-italic">لا يوجد محتوى نصي للرسالة</p>
                    {% endif %}
                </div>
                
                <!-- Attachments -->
                {% if message.attachments.count() > 0 %}
                <hr>
                <h6><i class="fas fa-paperclip me-2"></i>المرفقات:</h6>
                <div class="list-group">
                    {% for attachment in message.attachments %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-file me-2"></i>
                            <strong>{{ attachment.original_filename }}</strong>
                            <small class="text-muted">({{ "%.1f"|format(attachment.file_size / 1024) }} KB)</small>
                        </div>
                        <div>
                            <a href="#" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-download me-1"></i>تحميل
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Replies Section -->
        {% if replies %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-comments me-2"></i>
                    الردود ({{ replies|length }})
                </h5>
            </div>
            <div class="card-body">
                {% for reply in replies %}
                <div class="reply-item mb-3 p-3 border rounded">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <strong>{{ reply.user.full_name }}</strong>
                        <small class="text-muted">{{ reply.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                    </div>
                    <p class="mb-0">{{ reply.content|nl2br|safe }}</p>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- Reply Form -->
        {% if message.recipient_id == current_user.id %}
        <div class="card mt-3" id="replyForm" style="display: none;">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-reply me-2"></i>
                    إضافة رد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('reply_message', id=message.id) }}">
                    {{ reply_form.hidden_tag() }}
                    <div class="mb-3">
                        {{ reply_form.content.label(class="form-label") }}
                        {{ reply_form.content(class="form-control", rows="4", placeholder="اكتب ردك هنا...") }}
                    </div>
                    <div class="d-flex justify-content-between">
                        <div>
                            {{ reply_form.submit(class="btn btn-primary") }}
                            <button type="button" class="btn btn-secondary ms-2" onclick="hideReplyForm()">إلغاء</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-lg-4">
        <!-- Message Info -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الرسالة
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>الحالة:</strong></td>
                        <td>
                            {% if message.status.value == 'new' %}
                                <span class="badge bg-success">جديد</span>
                            {% elif message.status.value == 'read' %}
                                <span class="badge bg-info">مقروء</span>
                            {% elif message.status.value == 'replied' %}
                                <span class="badge bg-warning">تم الرد</span>
                            {% else %}
                                <span class="badge bg-secondary">مؤرشف</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>تاريخ الإرسال:</strong></td>
                        <td>{{ message.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    </tr>
                    {% if message.read_at %}
                    <tr>
                        <td><strong>تاريخ القراءة:</strong></td>
                        <td>{{ message.read_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    </tr>
                    {% endif %}
                    {% if message.replied_at %}
                    <tr>
                        <td><strong>تاريخ الرد:</strong></td>
                        <td>{{ message.replied_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td><strong>عدد المرفقات:</strong></td>
                        <td>{{ message.attachments.count() }}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    الإجراءات
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if message.recipient_id == current_user.id %}
                    <button class="btn btn-primary" onclick="showReplyForm()">
                        <i class="fas fa-reply me-2"></i>رد على الرسالة
                    </button>
                    <button class="btn btn-outline-info">
                        <i class="fas fa-share me-2"></i>إعادة توجيه
                    </button>
                    {% endif %}
                    <button class="btn btn-outline-warning">
                        <i class="fas fa-archive me-2"></i>أرشفة
                    </button>
                    <button class="btn btn-outline-success">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                    {% if current_user.has_permission('manage_users') %}
                    <button class="btn btn-outline-danger">
                        <i class="fas fa-trash me-2"></i>حذف
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .message-content {
        line-height: 1.6;
        font-size: 1.1rem;
    }
    
    .reply-item {
        background-color: #f8f9fa;
    }
    
    .reply-item:last-child {
        margin-bottom: 0 !important;
    }
</style>

<script>
function showReplyForm() {
    document.getElementById('replyForm').style.display = 'block';
    document.querySelector('#replyForm textarea').focus();
}

function hideReplyForm() {
    document.getElementById('replyForm').style.display = 'none';
}

// Auto-scroll to reply form if hash is present
if (window.location.hash === '#reply') {
    showReplyForm();
    document.getElementById('replyForm').scrollIntoView();
}
</script>
{% endblock %}
